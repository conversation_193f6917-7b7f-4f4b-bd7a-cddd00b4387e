import React, { useState } from 'react';
import { Search, Send, Paperclip, Smile, MoreVertical, Phone, Video, Info, Archive, Star, Clock } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

// Mock data for conversations
const mockConversations = [
  {
    id: 1,
    contact: {
      name: '<PERSON>',
      phone: '+1234567890',
      avatar: null,
      status: 'online'
    },
    lastMessage: 'Thank you for the quick response!',
    timestamp: '2 min ago',
    unreadCount: 0,
    isStarred: false,
    messages: [
      {
        id: 1,
        sender: 'contact',
        content: 'Hi, I have a question about my recent order',
        timestamp: '10:30 AM',
        status: 'delivered'
      },
      {
        id: 2,
        sender: 'agent',
        content: 'Hello! I\'d be happy to help you with your order. Can you please provide your order number?',
        timestamp: '10:32 AM',
        status: 'read'
      },
      {
        id: 3,
        sender: 'contact',
        content: 'Sure, it\'s #ORD-12345',
        timestamp: '10:33 AM',
        status: 'delivered'
      },
      {
        id: 4,
        sender: 'agent',
        content: 'Perfect! I can see your order here. It was shipped yesterday and should arrive by tomorrow. Here\'s your tracking link: track.example.com/12345',
        timestamp: '10:35 AM',
        status: 'read'
      },
      {
        id: 5,
        sender: 'contact',
        content: 'Thank you for the quick response!',
        timestamp: '10:36 AM',
        status: 'delivered'
      }
    ]
  },
  {
    id: 2,
    contact: {
      name: 'Jane Smith',
      phone: '+1234567891',
      avatar: null,
      status: 'offline'
    },
    lastMessage: 'When will the sale start?',
    timestamp: '15 min ago',
    unreadCount: 2,
    isStarred: true,
    messages: [
      {
        id: 1,
        sender: 'contact',
        content: 'Hi! I saw your announcement about the upcoming sale.',
        timestamp: '9:45 AM',
        status: 'delivered'
      },
      {
        id: 2,
        sender: 'contact',
        content: 'When will the sale start?',
        timestamp: '9:46 AM',
        status: 'delivered'
      }
    ]
  },
  {
    id: 3,
    contact: {
      name: 'Mike Johnson',
      phone: '+1234567892',
      avatar: null,
      status: 'online'
    },
    lastMessage: 'Great, thanks!',
    timestamp: '1 hour ago',
    unreadCount: 0,
    isStarred: false,
    messages: [
      {
        id: 1,
        sender: 'contact',
        content: 'Is my appointment confirmed for tomorrow?',
        timestamp: '8:30 AM',
        status: 'delivered'
      },
      {
        id: 2,
        sender: 'agent',
        content: 'Yes, your appointment is confirmed for tomorrow at 2:00 PM. We\'ll send you a reminder 1 hour before.',
        timestamp: '8:32 AM',
        status: 'read'
      },
      {
        id: 3,
        sender: 'contact',
        content: 'Great, thanks!',
        timestamp: '8:33 AM',
        status: 'delivered'
      }
    ]
  }
];

const quickReplies = [
  'Thank you for contacting us!',
  'I\'ll look into this for you.',
  'Is there anything else I can help you with?',
  'Your order has been processed.',
  'We appreciate your business!'
];

const AdminInbox: React.FC = () => {
  const [selectedConversation, setSelectedConversation] = useState(mockConversations[0]);
  const [messageInput, setMessageInput] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredConversations = mockConversations.filter(conv =>
    conv.contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.contact.phone.includes(searchTerm) ||
    conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSendMessage = () => {
    if (messageInput.trim()) {
      // TODO: Implement send message functionality
      console.log('Sending message:', messageInput);
      setMessageInput('');
    }
  };

  const handleQuickReply = (reply: string) => {
    setMessageInput(reply);
  };

  const getStatusColor = (status: string) => {
    return status === 'online' ? 'bg-green-500' : 'bg-gray-400';
  };

  const getMessageStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return '✓';
      case 'delivered':
        return '✓✓';
      case 'read':
        return '✓✓';
      default:
        return '';
    }
  };

  return (
    <div className="h-[calc(100vh-2rem)] flex">
      {/* Conversations List */}
      <div className="w-80 border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-3">Inbox</h2>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search conversations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Conversations */}
        <ScrollArea className="flex-1">
          <div className="p-2">
            {filteredConversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => setSelectedConversation(conversation)}
                className={`p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                  selectedConversation.id === conversation.id
                    ? 'bg-teal-50 border border-teal-200'
                    : 'hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className="relative">
                    <Avatar className="w-10 h-10">
                      <AvatarImage src={conversation.contact.avatar} />
                      <AvatarFallback>
                        {conversation.contact.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(conversation.contact.status)}`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {conversation.contact.name}
                      </p>
                      <div className="flex items-center space-x-1">
                        {conversation.isStarred && (
                          <Star className="w-3 h-3 text-yellow-500 fill-current" />
                        )}
                        <span className="text-xs text-gray-500">{conversation.timestamp}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-sm text-gray-600 truncate">{conversation.lastMessage}</p>
                      {conversation.unreadCount > 0 && (
                        <Badge className="bg-teal-600 text-white text-xs px-2 py-1 rounded-full">
                          {conversation.unreadCount}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="p-4 border-b border-gray-200 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Avatar className="w-10 h-10">
                <AvatarImage src={selectedConversation.contact.avatar} />
                <AvatarFallback>
                  {selectedConversation.contact.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(selectedConversation.contact.status)}`} />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{selectedConversation.contact.name}</h3>
              <p className="text-sm text-gray-500">{selectedConversation.contact.phone}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm">
              <Phone className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Video className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Info className="w-4 h-4" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <Star className="w-4 h-4 mr-2" />
                  {selectedConversation.isStarred ? 'Unstar' : 'Star'} Conversation
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Archive className="w-4 h-4 mr-2" />
                  Archive
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Messages */}
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {selectedConversation.messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'agent' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.sender === 'agent'
                      ? 'bg-teal-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <div className={`flex items-center justify-end mt-1 space-x-1 ${
                    message.sender === 'agent' ? 'text-teal-100' : 'text-gray-500'
                  }`}>
                    <span className="text-xs">{message.timestamp}</span>
                    {message.sender === 'agent' && (
                      <span className="text-xs">{getMessageStatusIcon(message.status)}</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>

        {/* Quick Replies */}
        <div className="px-4 py-2 border-t border-gray-100">
          <div className="flex space-x-2 overflow-x-auto">
            {quickReplies.map((reply, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => handleQuickReply(reply)}
                className="whitespace-nowrap"
              >
                {reply}
              </Button>
            ))}
          </div>
        </div>

        {/* Message Input */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm">
              <Paperclip className="w-4 h-4" />
            </Button>
            <div className="flex-1 relative">
              <Input
                placeholder="Type a message..."
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                className="pr-10"
              />
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2"
              >
                <Smile className="w-4 h-4" />
              </Button>
            </div>
            <Button
              onClick={handleSendMessage}
              disabled={!messageInput.trim()}
              className="bg-teal-600 hover:bg-teal-700"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminInbox;
