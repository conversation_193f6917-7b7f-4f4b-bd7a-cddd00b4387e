
import React from 'react';
import { Navigate } from 'react-router-dom';
import { authService, UserRole } from '@/utils/auth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole: UserRole;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRole, 
  redirectTo 
}) => {
  const isAuthenticated = authService.isAuthenticated();
  const hasRequiredRole = authService.hasRole(requiredRole);

  if (!isAuthenticated || !hasRequiredRole) {
    const defaultRedirect = requiredRole === 'admin' ? '/admin/login' : '/superadmin/login';
    return <Navigate to={redirectTo || defaultRedirect} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
