
import React from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { 
  LayoutDashboard, 
  MessageSquare, 
  Users, 
  Send, 
  Settings,
  Zap,
  FileText,
  Phone,
  BarChart3,
  CreditCard,
  LogOut,
  Building2,
  ScrollText,
  DollarSign,
  Activity
} from 'lucide-react';
import { authService, UserRole } from '@/utils/auth';
import { Button } from '@/components/ui/button';

interface SidebarProps {
  role: UserRole;
}

const Sidebar: React.FC<SidebarProps> = ({ role }) => {
  const navigate = useNavigate();
  const user = authService.getCurrentUser();

  const handleLogout = () => {
    authService.logout();
    navigate(role === 'admin' ? '/admin/login' : '/superadmin/login');
  };

  const adminMenuItems = [
    { icon: LayoutDashboard, label: 'Dashboard', path: '/admin/dashboard' },
    { icon: MessageSquare, label: 'Inbox', path: '/admin/inbox' },
    { icon: Send, label: 'Campaigns', path: '/admin/campaigns' },
    { icon: Users, label: 'Contacts', path: '/admin/contacts' },
    { icon: Zap, label: 'Automation', path: '/admin/automation' },
    { icon: FileText, label: 'Templates', path: '/admin/templates' },
    { icon: Phone, label: 'Phone Numbers', path: '/admin/phone-numbers' },
    { icon: BarChart3, label: 'Analytics', path: '/admin/analytics' },
    { icon: CreditCard, label: 'Billing', path: '/admin/billing' },
    { icon: Settings, label: 'Settings', path: '/admin/settings' },
  ];

  const superAdminMenuItems = [
    { icon: LayoutDashboard, label: 'Dashboard', path: '/superadmin/dashboard' },
    { icon: Building2, label: 'Businesses', path: '/superadmin/businesses' },
    { icon: FileText, label: 'Templates', path: '/superadmin/templates' },
    { icon: BarChart3, label: 'Analytics', path: '/superadmin/analytics' },
    { icon: DollarSign, label: 'Plans', path: '/superadmin/plans' },
    { icon: ScrollText, label: 'Logs', path: '/superadmin/logs' },
    { icon: Settings, label: 'Settings', path: '/superadmin/settings' },
  ];

  const menuItems = role === 'admin' ? adminMenuItems : superAdminMenuItems;

  return (
    <div className="flex flex-col h-full bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex items-center px-6 py-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-teal-600 rounded-lg flex items-center justify-center">
            <MessageSquare className="h-5 w-5 text-white" />
          </div>
          <span className="text-xl font-bold text-gray-900">AyuChat</span>
        </div>
      </div>

      {/* User Info */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center">
            <span className="text-teal-600 font-medium">
              {user?.name.charAt(0).toUpperCase()}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user?.name}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {user?.company || user?.email}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-1">
        {menuItems.map((item) => (
          <NavLink
            key={item.path}
            to={item.path}
            className={({ isActive }) =>
              `flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                isActive
                  ? 'bg-teal-50 text-teal-700 border-r-2 border-teal-600'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`
            }
          >
            <item.icon className="mr-3 h-5 w-5" />
            {item.label}
          </NavLink>
        ))}
      </nav>

      {/* Logout */}
      <div className="p-4 border-t border-gray-200">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleLogout}
          className="w-full justify-start text-gray-600 hover:text-gray-900"
        >
          <LogOut className="mr-3 h-4 w-4" />
          Logout
        </Button>
      </div>
    </div>
  );
};

export default Sidebar;
