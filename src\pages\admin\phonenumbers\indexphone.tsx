import React, { useState } from 'react';
import { Plus, Phone, CheckCircle, AlertCircle, Settings, Eye, EyeOff, Copy, Trash2, <PERSON>fresh<PERSON><PERSON>, Webhook, Key, Shield } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

// Mock data for phone numbers
const mockPhoneNumbers = [
  {
    id: 1,
    phoneNumber: '+1234567890',
    displayName: 'Main Business Line',
    status: 'Connected',
    businessId: 'BUSINESS_ID_123',
    appId: 'APP_ID_456',
    phoneNumberId: 'PHONE_ID_789',
    accessToken: 'EAABwzLixnjYBO...',
    webhookUrl: 'https://your-domain.com/webhook',
    verifyToken: 'your_verify_token_123',
    lastVerified: '2024-03-15 10:30 AM',
    messagesSent: 1250,
    messagesReceived: 890
  },
  {
    id: 2,
    phoneNumber: '+1234567891',
    displayName: 'Support Line',
    status: 'Pending',
    businessId: 'BUSINESS_ID_456',
    appId: 'APP_ID_789',
    phoneNumberId: 'PHONE_ID_012',
    accessToken: 'EAABwzLixnjYBO...',
    webhookUrl: 'https://your-domain.com/webhook',
    verifyToken: 'your_verify_token_456',
    lastVerified: null,
    messagesSent: 0,
    messagesReceived: 0
  }
];

const AdminPhoneNumbers: React.FC = () => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [showTokens, setShowTokens] = useState<{[key: number]: boolean}>({});
  const { toast } = useToast();

  const [newPhoneNumber, setNewPhoneNumber] = useState({
    displayName: '',
    businessId: '',
    appId: '',
    phoneNumberId: '',
    accessToken: '',
    webhookUrl: '',
    verifyToken: ''
  });

  const handleAddPhoneNumber = () => {
    // TODO: Implement API validation and connection
    toast({
      title: "Phone Number Added",
      description: "Your WhatsApp Business phone number has been added successfully.",
    });
    setIsAddDialogOpen(false);
    setNewPhoneNumber({
      displayName: '',
      businessId: '',
      appId: '',
      phoneNumberId: '',
      accessToken: '',
      webhookUrl: '',
      verifyToken: ''
    });
  };

  const handleTestConnection = (phoneId: number) => {
    // TODO: Implement connection test
    toast({
      title: "Connection Test",
      description: "Testing connection to WhatsApp Business API...",
    });
  };

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Text copied to clipboard.",
    });
  };

  const toggleTokenVisibility = (phoneId: number) => {
    setShowTokens(prev => ({
      ...prev,
      [phoneId]: !prev[phoneId]
    }));
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Connected':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Connected</Badge>;
      case 'Pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><AlertCircle className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'Error':
        return <Badge className="bg-red-100 text-red-800"><AlertCircle className="w-3 h-3 mr-1" />Error</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const maskToken = (token: string) => {
    if (token.length <= 8) return token;
    return token.substring(0, 8) + '...';
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Phone Numbers</h1>
          <p className="text-gray-600 mt-1">Manage your WhatsApp Business API connections</p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-teal-600 hover:bg-teal-700">
              <Plus className="w-4 h-4 mr-2" />
              Add Phone Number
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add WhatsApp Business Phone Number</DialogTitle>
              <DialogDescription>
                Connect your WhatsApp Business API credentials to start sending messages
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <Alert>
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  Your API credentials are encrypted and stored securely. We never share your data with third parties.
                </AlertDescription>
              </Alert>
              
              <div>
                <Label htmlFor="display-name">Display Name</Label>
                <Input
                  id="display-name"
                  value={newPhoneNumber.displayName}
                  onChange={(e) => setNewPhoneNumber({...newPhoneNumber, displayName: e.target.value})}
                  placeholder="e.g., Main Business Line"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="business-id">Business ID</Label>
                  <Input
                    id="business-id"
                    value={newPhoneNumber.businessId}
                    onChange={(e) => setNewPhoneNumber({...newPhoneNumber, businessId: e.target.value})}
                    placeholder="Your WhatsApp Business ID"
                  />
                </div>
                <div>
                  <Label htmlFor="app-id">App ID</Label>
                  <Input
                    id="app-id"
                    value={newPhoneNumber.appId}
                    onChange={(e) => setNewPhoneNumber({...newPhoneNumber, appId: e.target.value})}
                    placeholder="Your Facebook App ID"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="phone-number-id">Phone Number ID</Label>
                <Input
                  id="phone-number-id"
                  value={newPhoneNumber.phoneNumberId}
                  onChange={(e) => setNewPhoneNumber({...newPhoneNumber, phoneNumberId: e.target.value})}
                  placeholder="Your WhatsApp Phone Number ID"
                />
              </div>
              
              <div>
                <Label htmlFor="access-token">Access Token</Label>
                <Input
                  id="access-token"
                  type="password"
                  value={newPhoneNumber.accessToken}
                  onChange={(e) => setNewPhoneNumber({...newPhoneNumber, accessToken: e.target.value})}
                  placeholder="Your WhatsApp Business API Access Token"
                />
              </div>
              
              <div>
                <Label htmlFor="webhook-url">Webhook URL</Label>
                <Input
                  id="webhook-url"
                  value={newPhoneNumber.webhookUrl}
                  onChange={(e) => setNewPhoneNumber({...newPhoneNumber, webhookUrl: e.target.value})}
                  placeholder="https://your-domain.com/webhook"
                />
              </div>
              
              <div>
                <Label htmlFor="verify-token">Verify Token</Label>
                <Input
                  id="verify-token"
                  value={newPhoneNumber.verifyToken}
                  onChange={(e) => setNewPhoneNumber({...newPhoneNumber, verifyToken: e.target.value})}
                  placeholder="Your webhook verify token"
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddPhoneNumber} className="bg-teal-600 hover:bg-teal-700">
                  Add & Test Connection
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Numbers</p>
                <p className="text-2xl font-bold text-gray-900">{mockPhoneNumbers.length}</p>
              </div>
              <Phone className="h-8 w-8 text-teal-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Connected</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockPhoneNumbers.filter(p => p.status === 'Connected').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Messages Sent</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockPhoneNumbers.reduce((sum, p) => sum + p.messagesSent, 0).toLocaleString()}
                </p>
              </div>
              <Webhook className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Messages Received</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockPhoneNumbers.reduce((sum, p) => sum + p.messagesReceived, 0).toLocaleString()}
                </p>
              </div>
              <Key className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Phone Numbers List */}
      <div className="space-y-4">
        {mockPhoneNumbers.map((phoneNumber) => (
          <Card key={phoneNumber.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Phone className="w-4 h-4 text-teal-600" />
                    {phoneNumber.displayName}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {phoneNumber.phoneNumber}
                  </CardDescription>
                </div>
                {getStatusBadge(phoneNumber.status)}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Connection Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Business ID</Label>
                      <div className="flex items-center gap-2">
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">{phoneNumber.businessId}</code>
                        <Button variant="ghost" size="sm" onClick={() => handleCopyToClipboard(phoneNumber.businessId)}>
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Phone Number ID</Label>
                      <div className="flex items-center gap-2">
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">{phoneNumber.phoneNumberId}</code>
                        <Button variant="ghost" size="sm" onClick={() => handleCopyToClipboard(phoneNumber.phoneNumberId)}>
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Access Token</Label>
                      <div className="flex items-center gap-2">
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                          {showTokens[phoneNumber.id] ? phoneNumber.accessToken : maskToken(phoneNumber.accessToken)}
                        </code>
                        <Button variant="ghost" size="sm" onClick={() => toggleTokenVisibility(phoneNumber.id)}>
                          {showTokens[phoneNumber.id] ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleCopyToClipboard(phoneNumber.accessToken)}>
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Webhook URL</Label>
                      <div className="flex items-center gap-2">
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded truncate">{phoneNumber.webhookUrl}</code>
                        <Button variant="ghost" size="sm" onClick={() => handleCopyToClipboard(phoneNumber.webhookUrl)}>
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Last Verified</Label>
                      <p className="text-sm text-gray-900">{phoneNumber.lastVerified || 'Never'}</p>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Sent</Label>
                        <p className="text-lg font-bold text-green-600">{phoneNumber.messagesSent.toLocaleString()}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Received</Label>
                        <p className="text-lg font-bold text-blue-600">{phoneNumber.messagesReceived.toLocaleString()}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-4 border-t">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleTestConnection(phoneNumber.id)}
                  >
                    <RefreshCw className="w-4 h-4 mr-1" />
                    Test Connection
                  </Button>
                  <Button variant="outline" size="sm">
                    <Settings className="w-4 h-4 mr-1" />
                    Configure
                  </Button>
                  <Button variant="outline" size="sm">
                    <Webhook className="w-4 h-4 mr-1" />
                    Webhook Logs
                  </Button>
                  <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700 ml-auto">
                    <Trash2 className="w-4 h-4 mr-1" />
                    Remove
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default AdminPhoneNumbers;
