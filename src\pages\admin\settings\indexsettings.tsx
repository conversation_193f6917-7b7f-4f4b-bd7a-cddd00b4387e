import React, { useState } from 'react';
import { Save, Upload, User, Building, Globe, Bell, Shield, Key, Trash2, Edit } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';

const AdminSettings: React.FC = () => {
  const { toast } = useToast();

  // Business Profile Settings
  const [businessProfile, setBusinessProfile] = useState({
    businessName: 'AyuChat Inc.',
    description: 'Leading WhatsApp marketing automation platform',
    website: 'https://ayuchat.com',
    email: '<EMAIL>',
    phone: '+1234567890',
    address: '123 Business St, City, State 12345',
    logo: null as File | null,
    timezone: 'America/New_York',
    language: 'en'
  });

  // Notification Settings
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    smsNotifications: false,
    campaignUpdates: true,
    systemAlerts: true,
    weeklyReports: true,
    newContacts: false,
    failedMessages: true
  });

  // Security Settings
  const [security, setSecurity] = useState({
    twoFactorAuth: false,
    sessionTimeout: '24',
    ipWhitelist: '',
    apiAccess: true
  });

  // WhatsApp Settings
  const [whatsappSettings, setWhatsappSettings] = useState({
    defaultTemplate: 'Welcome Message',
    autoReply: true,
    businessHours: {
      enabled: true,
      start: '09:00',
      end: '17:00',
      timezone: 'America/New_York'
    },
    awayMessage: 'Thank you for your message. We will get back to you during business hours.'
  });

  const handleSaveProfile = () => {
    // TODO: Implement API call to save business profile
    toast({
      title: "Profile Updated",
      description: "Your business profile has been updated successfully.",
    });
  };

  const handleSaveNotifications = () => {
    // TODO: Implement API call to save notification settings
    toast({
      title: "Notifications Updated",
      description: "Your notification preferences have been saved.",
    });
  };

  const handleSaveSecurity = () => {
    // TODO: Implement API call to save security settings
    toast({
      title: "Security Updated",
      description: "Your security settings have been updated.",
    });
  };

  const handleSaveWhatsApp = () => {
    // TODO: Implement API call to save WhatsApp settings
    toast({
      title: "WhatsApp Settings Updated",
      description: "Your WhatsApp preferences have been saved.",
    });
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setBusinessProfile({ ...businessProfile, logo: file });
      toast({
        title: "Logo Uploaded",
        description: "Your business logo has been uploaded successfully.",
      });
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600 mt-1">Manage your account and business preferences</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Business Profile */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Business Profile
              </CardTitle>
              <CardDescription>
                Update your business information and branding
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Logo Upload */}
              <div className="flex items-center space-x-4">
                <Avatar className="w-16 h-16">
                  <AvatarImage src={businessProfile.logo ? URL.createObjectURL(businessProfile.logo) : undefined} />
                  <AvatarFallback>
                    <Building className="w-8 h-8" />
                  </AvatarFallback>
                </Avatar>
                <div>
                  <Label htmlFor="logo-upload" className="cursor-pointer">
                    <Button variant="outline" size="sm" asChild>
                      <span>
                        <Upload className="w-4 h-4 mr-2" />
                        Upload Logo
                      </span>
                    </Button>
                  </Label>
                  <Input
                    id="logo-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleLogoUpload}
                    className="hidden"
                  />
                  <p className="text-sm text-gray-500 mt-1">PNG, JPG up to 2MB</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="business-name">Business Name</Label>
                  <Input
                    id="business-name"
                    value={businessProfile.businessName}
                    onChange={(e) => setBusinessProfile({...businessProfile, businessName: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={businessProfile.website}
                    onChange={(e) => setBusinessProfile({...businessProfile, website: e.target.value})}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={businessProfile.description}
                  onChange={(e) => setBusinessProfile({...businessProfile, description: e.target.value})}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={businessProfile.email}
                    onChange={(e) => setBusinessProfile({...businessProfile, email: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={businessProfile.phone}
                    onChange={(e) => setBusinessProfile({...businessProfile, phone: e.target.value})}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  value={businessProfile.address}
                  onChange={(e) => setBusinessProfile({...businessProfile, address: e.target.value})}
                  rows={2}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="timezone">Timezone</Label>
                  <Select value={businessProfile.timezone} onValueChange={(value) => setBusinessProfile({...businessProfile, timezone: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="America/New_York">Eastern Time</SelectItem>
                      <SelectItem value="America/Chicago">Central Time</SelectItem>
                      <SelectItem value="America/Denver">Mountain Time</SelectItem>
                      <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                      <SelectItem value="UTC">UTC</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="language">Language</Label>
                  <Select value={businessProfile.language} onValueChange={(value) => setBusinessProfile({...businessProfile, language: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="es">Spanish</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                      <SelectItem value="de">German</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button onClick={handleSaveProfile} className="bg-teal-600 hover:bg-teal-700">
                <Save className="w-4 h-4 mr-2" />
                Save Profile
              </Button>
            </CardContent>
          </Card>

          {/* WhatsApp Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                WhatsApp Settings
              </CardTitle>
              <CardDescription>
                Configure your WhatsApp business preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="default-template">Default Template</Label>
                <Select value={whatsappSettings.defaultTemplate} onValueChange={(value) => setWhatsappSettings({...whatsappSettings, defaultTemplate: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Welcome Message">Welcome Message</SelectItem>
                    <SelectItem value="Order Confirmation">Order Confirmation</SelectItem>
                    <SelectItem value="Support Response">Support Response</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto-reply">Auto Reply</Label>
                  <p className="text-sm text-gray-500">Automatically respond to new messages</p>
                </div>
                <Switch
                  id="auto-reply"
                  checked={whatsappSettings.autoReply}
                  onCheckedChange={(checked) => setWhatsappSettings({...whatsappSettings, autoReply: checked})}
                />
              </div>

              <Separator />

              <div>
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <Label htmlFor="business-hours">Business Hours</Label>
                    <p className="text-sm text-gray-500">Set your business operating hours</p>
                  </div>
                  <Switch
                    id="business-hours"
                    checked={whatsappSettings.businessHours.enabled}
                    onCheckedChange={(checked) => setWhatsappSettings({
                      ...whatsappSettings,
                      businessHours: { ...whatsappSettings.businessHours, enabled: checked }
                    })}
                  />
                </div>

                {whatsappSettings.businessHours.enabled && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="start-time">Start Time</Label>
                      <Input
                        id="start-time"
                        type="time"
                        value={whatsappSettings.businessHours.start}
                        onChange={(e) => setWhatsappSettings({
                          ...whatsappSettings,
                          businessHours: { ...whatsappSettings.businessHours, start: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="end-time">End Time</Label>
                      <Input
                        id="end-time"
                        type="time"
                        value={whatsappSettings.businessHours.end}
                        onChange={(e) => setWhatsappSettings({
                          ...whatsappSettings,
                          businessHours: { ...whatsappSettings.businessHours, end: e.target.value }
                        })}
                      />
                    </div>
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="away-message">Away Message</Label>
                <Textarea
                  id="away-message"
                  value={whatsappSettings.awayMessage}
                  onChange={(e) => setWhatsappSettings({...whatsappSettings, awayMessage: e.target.value})}
                  rows={3}
                  placeholder="Message to send when outside business hours"
                />
              </div>

              <Button onClick={handleSaveWhatsApp} className="bg-teal-600 hover:bg-teal-700">
                <Save className="w-4 h-4 mr-2" />
                Save WhatsApp Settings
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar Settings */}
        <div className="space-y-6">
          {/* Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications
              </CardTitle>
              <CardDescription>
                Manage your notification preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="email-notifications">Email Notifications</Label>
                <Switch
                  id="email-notifications"
                  checked={notifications.emailNotifications}
                  onCheckedChange={(checked) => setNotifications({...notifications, emailNotifications: checked})}
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="campaign-updates">Campaign Updates</Label>
                <Switch
                  id="campaign-updates"
                  checked={notifications.campaignUpdates}
                  onCheckedChange={(checked) => setNotifications({...notifications, campaignUpdates: checked})}
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="system-alerts">System Alerts</Label>
                <Switch
                  id="system-alerts"
                  checked={notifications.systemAlerts}
                  onCheckedChange={(checked) => setNotifications({...notifications, systemAlerts: checked})}
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="weekly-reports">Weekly Reports</Label>
                <Switch
                  id="weekly-reports"
                  checked={notifications.weeklyReports}
                  onCheckedChange={(checked) => setNotifications({...notifications, weeklyReports: checked})}
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="failed-messages">Failed Messages</Label>
                <Switch
                  id="failed-messages"
                  checked={notifications.failedMessages}
                  onCheckedChange={(checked) => setNotifications({...notifications, failedMessages: checked})}
                />
              </div>
              <Button onClick={handleSaveNotifications} variant="outline" className="w-full">
                <Save className="w-4 h-4 mr-2" />
                Save Notifications
              </Button>
            </CardContent>
          </Card>

          {/* Security */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security
              </CardTitle>
              <CardDescription>
                Manage your account security settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="two-factor">Two-Factor Auth</Label>
                  <p className="text-xs text-gray-500">Add extra security to your account</p>
                </div>
                <Switch
                  id="two-factor"
                  checked={security.twoFactorAuth}
                  onCheckedChange={(checked) => setSecurity({...security, twoFactorAuth: checked})}
                />
              </div>
              
              <div>
                <Label htmlFor="session-timeout">Session Timeout (hours)</Label>
                <Select value={security.sessionTimeout} onValueChange={(value) => setSecurity({...security, sessionTimeout: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 hour</SelectItem>
                    <SelectItem value="8">8 hours</SelectItem>
                    <SelectItem value="24">24 hours</SelectItem>
                    <SelectItem value="168">1 week</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="api-access">API Access</Label>
                <Switch
                  id="api-access"
                  checked={security.apiAccess}
                  onCheckedChange={(checked) => setSecurity({...security, apiAccess: checked})}
                />
              </div>

              <Button onClick={handleSaveSecurity} variant="outline" className="w-full">
                <Save className="w-4 h-4 mr-2" />
                Save Security
              </Button>
            </CardContent>
          </Card>

          {/* Danger Zone */}
          <Card className="border-red-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600">
                <Trash2 className="h-5 w-5" />
                Danger Zone
              </CardTitle>
              <CardDescription>
                Irreversible and destructive actions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button variant="outline" className="w-full text-red-600 border-red-200 hover:bg-red-50">
                <Key className="w-4 h-4 mr-2" />
                Reset API Keys
              </Button>
              <Button variant="outline" className="w-full text-red-600 border-red-200 hover:bg-red-50">
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Account
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AdminSettings;
